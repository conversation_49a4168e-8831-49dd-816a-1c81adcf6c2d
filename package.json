{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "autoprefixer": "10.4.20", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "disconnect": "^1.2.2", "lucide-react": "^0.468.0", "next": "latest", "next-themes": "^0.4.3", "nuqs": "^2.4.3", "prettier": "^3.3.3", "react": "19.0.0", "react-dom": "19.0.0"}, "devDependencies": {"@types/node": "22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "19.0.2", "postcss": "8.4.49", "prettier-plugin-tailwindcss": "^0.6.11", "tailwind-merge": "^2.5.2", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.2"}}