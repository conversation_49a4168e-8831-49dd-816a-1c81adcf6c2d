import Link from 'next/link'
import { Badge } from './ui/badge'
import { But<PERSON> } from './ui/button'

export function EnvVarWarning() {
  return (
    <div className="flex items-center gap-4">
      <Badge variant={'outline'} className="font-normal">
        Supabase environment variables required
      </Badge>
      <div className="flex gap-2">
        <Button
          asChild
          size="sm"
          variant={'outline'}
          disabled
          className="pointer-events-none cursor-none opacity-75"
        >
          <Link href="/sign-in">Sign in</Link>
        </Button>
        <Button
          asChild
          size="sm"
          variant={'default'}
          disabled
          className="pointer-events-none cursor-none opacity-75"
        >
          <Link href="/sign-up">Sign up</Link>
        </Button>
      </div>
    </div>
  )
}
